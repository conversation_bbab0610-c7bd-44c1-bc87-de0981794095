import 'package:flutter/material.dart';

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [BoxShadow(color: Colors.grey.shade300, blurRadius: 4)],
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {},
                  ),
                  const CircleAvatar(
                    backgroundImage: NetworkImage(
                      'https://images.unsplash.com/photo-1603415526960-f7e0328f1e88',
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('User', style: TextStyle(fontWeight: FontWeight.bold)),
                      Text('Verified', style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                  const Spacer(),
                  Icon<PERSON>utton(icon: const Icon(Icons.call), onPressed: () {}),
                  IconButton(icon: const Icon(Icons.videocam), onPressed: () {}),
                ],
              ),
            ),

            const SizedBox(height: 10),

            // Chat Body
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                children: [
                  // Image Message
                  Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          'https://images.unsplash.com/photo-1549921296-3a6b51bfbf84',
                          height: 200,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'Night',
                          style: TextStyle(color: Colors.white, backgroundColor: Colors.black54),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: const [
                      Expanded(
                        child: MessageBubble(
                          text: 'Memorable moments shared',
                          tag: 'Current',
                        ),
                      ),
                      Icon(Icons.thumb_up_alt_outlined, size: 16),
                    ],
                  ),
                  const SizedBox(height: 12),

                  const Text('Present Morning', style: TextStyle(fontSize: 12, color: Colors.grey)),

                  const SizedBox(height: 8),
                  Row(
                    children: const [
                      Icon(Icons.thumb_up_alt_outlined, size: 16),
                      SizedBox(width: 4),
                      Expanded(
                        child: MessageBubble(text: "Let's plan", tag: 'Mornin'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.pause_circle_outline),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: const [
                              Text('Morn', style: TextStyle(fontSize: 14)),
                              Spacer(),
                              Text('Reply', style: TextStyle(fontSize: 12, backgroundColor: Colors.black12)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),
                  Row(
                    children: const [
                      Expanded(
                        child: MessageBubble(text: "Excite Meetin"),
                      ),
                      Icon(Icons.thumb_up_alt_outlined, size: 16),
                    ],
                  ),
                ],
              ),
            ),

            // Input Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                border: const Border(
                  top: BorderSide(color: Colors.grey),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.grid_view),
                  const SizedBox(width: 10),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: "Type message here",
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(Icons.mic),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MessageBubble extends StatelessWidget {
  final String text;
  final String? tag;

  const MessageBubble({super.key, required this.text, this.tag});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(text, style: const TextStyle(fontSize: 14)),
          if (tag != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(tag!, style: const TextStyle(fontSize: 10, color: Colors.black54)),
            ),
        ],
      ),
    );
  }
}
