import 'package:flutter/material.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  void _onHorizontalDrag(BuildContext context, DragEndDetails details) {
    // Detect left swipe (velocity negative)
    if (details.primaryVelocity != null && details.primaryVelocity! < 0) {
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragEnd: (details) => _onHorizontalDrag(context, details),
      child: const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo image
                Image(
                  image: AssetImage('assets/logo.png'), 
                  width: 180,
                  height: 180,
                ),
                SizedBox(height: 32),
                
                // Title
                Text(
                  'GlobalRootsx',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 12),

                // Subtitle
                Text(
                  'Explore diverse events worldwide',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
