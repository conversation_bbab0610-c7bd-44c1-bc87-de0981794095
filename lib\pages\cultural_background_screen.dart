import 'package:flutter/material.dart';

class CulturalBackgroundScreen extends StatefulWidget {
  const CulturalBackgroundScreen({super.key});

  @override
  State<CulturalBackgroundScreen> createState() => _CulturalBackgroundScreenState();
}

class _CulturalBackgroundScreenState extends State<CulturalBackgroundScreen> {
  final List<String> _selectedCultures = [];
  
  final List<Map<String, String>> _cultures = [
    {'name': 'African', 'flag': '🌍'},
    {'name': 'Asian', 'flag': '🌏'},
    {'name': 'European', 'flag': '🌍'},
    {'name': 'Latin American', 'flag': '🌎'},
    {'name': 'Middle Eastern', 'flag': '🌍'},
    {'name': 'North American', 'flag': '🌎'},
    {'name': 'Caribbean', 'flag': '🏝️'},
    {'name': 'Pacific Islander', 'flag': '🏝️'},
    {'name': 'Indigenous', 'flag': '🪶'},
    {'name': 'Mixed Heritage', 'flag': '🌐'},
    {'name': 'Arab', 'flag': '🕌'},
    {'name': 'Jewish', 'flag': '✡️'},
    {'name': 'Hispanic', 'flag': '🌮'},
    {'name': 'Mediterranean', 'flag': '🫒'},
    {'name': 'Nordic', 'flag': '❄️'},
    {'name': 'Slavic', 'flag': '🏰'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Cultural Background',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _selectedCultures.isNotEmpty ? () {
              Navigator.of(context).pop(_selectedCultures);
            } : null,
            child: Text(
              'Save',
              style: TextStyle(
                color: _selectedCultures.isNotEmpty ? Colors.blue : Colors.grey,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header Info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Select your cultural background',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You can select multiple cultures that represent your heritage',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Selected Count
          if (_selectedCultures.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              color: Colors.blue[50],
              child: Text(
                '${_selectedCultures.length} culture${_selectedCultures.length > 1 ? 's' : ''} selected',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          
          // Cultures List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _cultures.length,
              itemBuilder: (context, index) {
                final culture = _cultures[index];
                final isSelected = _selectedCultures.contains(culture['name']);
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? Colors.blue : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ListTile(
                    leading: Text(
                      culture['flag']!,
                      style: const TextStyle(fontSize: 24),
                    ),
                    title: Text(
                      culture['name']!,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? Colors.blue : Colors.black,
                      ),
                    ),
                    trailing: isSelected
                        ? Icon(
                            Icons.check_circle,
                            color: Colors.blue,
                          )
                        : Icon(
                            Icons.circle_outlined,
                            color: Colors.grey[400],
                          ),
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedCultures.remove(culture['name']);
                        } else {
                          _selectedCultures.add(culture['name']!);
                        }
                      });
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
