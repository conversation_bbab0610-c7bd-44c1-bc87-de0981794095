import 'package:flutter/material.dart';
import 'community_screen.dart';
import 'events_search_screen.dart';
import 'events_page.dart';
import '../widgets/explore_widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    if (index == 1) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => EventsSearchScreen(
            scrollController: ScrollController(),
          ),
        ),
      );
    } else if (index == 2) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const EventsPage()),
      );
    } else if (index == 3) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const CommunityScreen()),
      );
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    const featuredImage =
        'https://images.unsplash.com/photo-1742201949659-ce186667aaaf?q=400&w=400&auto=format&fit=crop';
    const profileImage = 'https://randomuser.me/api/portraits/men/32.jpg';
    const exploreImages = [
      'https://images.unsplash.com/photo-1717913490699-e4600b1b1630?fm=jpg&q=400&w=400',
      'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
      'https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80',
      'https://plus.unsplash.com/premium_photo-1667031519192-ba1ed681751d?fm=jpg&q=400&w=400',
    ];

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Bar
                Row(
                  children: [
                    CircleAvatar(
                      backgroundImage: NetworkImage(profileImage),
                      radius: 18,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Welcome to GlobalRoots!',
                        style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: () {},
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  'Explore your roots with us!',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                ),
                const SizedBox(height: 8),
                // Search Bar
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => DraggableScrollableSheet(
                              expand: false,
                              initialChildSize: 0.95,
                              minChildSize: 0.7,
                              maxChildSize: 0.95,
                              builder: (context, scrollController) => Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.vertical(top: Radius.circular(24)),
                                ),
                                child: EventsSearchScreen(
                                  scrollController: scrollController,
                                ),
                              ),
                            ),
                          );
                        },
                        child: AbsorbPointer(
                          child: TextField(
                            decoration: InputDecoration(
                              hintText: 'Ex: Hometown dish',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              contentPadding:
                                  const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.filter_list, color: Colors.white),
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                const Text(
                  'Featured profile of the day',
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.network(
                          featuredImage,
                          height: 200,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const Center(child: Icon(Icons.broken_image, size: 50)),
                        ),
                      ),
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: const [
                              Icon(Icons.star, color: Colors.amber, size: 16),
                              SizedBox(width: 2),
                              Text('4.2', style: TextStyle(color: Colors.white, fontSize: 12)),
                            ],
                          ),
                        ),
                      ),
                      const Positioned(
                        top: 8,
                        right: 8,
                        child: Icon(Icons.favorite_border, color: Colors.white),
                      ),
                      Positioned(
                        bottom: 8,
                        left: 16,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: const [
                            Text(
                              'Authentic Home',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  shadows: [Shadow(color: Colors.black, blurRadius: 2)]),
                            ),
                            Text(
                              'Verified · Local · Community',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  shadows: [Shadow(color: Colors.black, blurRadius: 2)]),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        bottom: 8,
                        right: 16,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8)),
                          ),
                          onPressed: () {},
                          child: const Text('Join now', style: TextStyle(color: Colors.white)),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    Text('Explore', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                    Text('Discover', style: TextStyle(color: Colors.grey, fontSize: 14)),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    ExploreCategory(icon: Icons.location_on, label: 'Local'),
                    ExploreCategory(icon: Icons.history_edu, label: 'Traditional'),
                    ExploreCategory(icon: Icons.event, label: 'Events'),
                    ExploreCategory(icon: Icons.restaurant, label: 'Culinary'),
                  ],
                ),
                const SizedBox(height: 16),
                GridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1.2,
                  children: [
                    ExploreCard(
                      imageUrl: exploreImages[0],
                      title: 'Homemade Family Recipes',
                      subtitle: '30',
                      favorite: false,
                    ),
                    ExploreCard(
                      imageUrl: exploreImages[1],
                      subtitle: '2h',
                      title: 'Regional Specialties',
                      
                      favorite: true,
                    ),
                    ExploreCard(
                      imageUrl: exploreImages[2],
                      subtitle: '45',
                      title: 'Cultural',
                      
                      favorite: false,
                    ),
                    ExploreCard(
                      imageUrl: exploreImages[3],
                      subtitle: '1h 30 min',
                      title: 'Local Flavors Galore',
                      favorite: true,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Column(
                      //   children: const [
                      //     Text('59', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                      //     Text('Interactions', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      //   ],
                      // ),
                      // GestureDetector(
                      //   onTap: () {
                      //     Navigator.of(context).push(
                      //       MaterialPageRoute(builder: (context) => const CommunityScreen()),
                      //     );
                      //   },
                      //   child: Column(
                      //     children: const [
                      //       Text('Connect', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                      //       Text('Community', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      //     ],
                      //   ),
                      // ),
                      // Column(
                      //   children: const [
                      //     Text('Events', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                      //     Text('Discoveries', style: TextStyle(fontSize: 12, color: Colors.grey)),
                      //   ],
                      // ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Disco'),
          BottomNavigationBarItem(icon: Icon(Icons.event), label: 'Events'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
        selectedItemColor: Colors.black,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
