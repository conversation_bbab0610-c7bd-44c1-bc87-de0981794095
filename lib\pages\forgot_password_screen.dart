import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/auth_service.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  bool _isLoading = false;
  String? _error;
  String? _success;

  Future<void> _sendVerification() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _success = null;
    });
    try {
      // You may need to implement this endpoint in AuthService
      final result = await AuthService.forgotPassword(email: _emailController.text.trim());
      setState(() {
        _isLoading = false;
      });
      if (result['statusCode'] == 200 || result['statusCode'] == 201) {
        setState(() {
          _success = 'Verification code sent to your email.';
        });
      } else {
        final error = jsonDecode(result['body']);
        setState(() {
          _error = error['message'] ?? 'Failed to send verification code.';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'An error occurred: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 4,
        shadowColor: Colors.black.withOpacity(0.15),
        iconTheme: const IconThemeData(color: Colors.black),
        title: const Text('Forgot Password', style: TextStyle(color: Colors.black)),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 8),
            Image(
              image: AssetImage('assets/logo.png'),
              height: 90,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 16),
            const Text('GlobalRoots', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 26)),
            const SizedBox(height: 24),
            const Text('Enter your email to receive a verification code.', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 24),
            TextField(
              controller: _emailController,
              decoration: InputDecoration(
                hintText: 'Email',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            if (_error != null) ...[
              Text(_error!, style: const TextStyle(color: Colors.red)),
              const SizedBox(height: 8),
            ],
            if (_success != null) ...[
              Text(_success!, style: const TextStyle(color: Colors.green)),
              const SizedBox(height: 8),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                onPressed: _isLoading ? null : _sendVerification,
                child: _isLoading
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2.0,
                      )
                    : const Text('Send Verification Code', style: TextStyle(color: Colors.white, fontSize: 16)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
