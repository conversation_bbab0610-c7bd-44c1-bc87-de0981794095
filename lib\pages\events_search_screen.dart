import 'package:flutter/material.dart';
import 'home_screen.dart';
import 'chat_screen.dart';

class EventsSearchScreen extends StatefulWidget {
  const EventsSearchScreen({super.key, required ScrollController scrollController});

  @override
  State<EventsSearchScreen> createState() => _EventsSearchScreenState();
}

class _EventsSearchScreenState extends State<EventsSearchScreen> {
  int _selectedIndex = 1;

  void _onItemTapped(int index) {
    if (index == 0) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final categories = [
      {'icon': Icons.music_note, 'label': 'Music concerts near'},
      {'icon': Icons.local_bar, 'label': 'Exciting parties to'},
      {'icon': Icons.museum, 'label': 'Explore local museums'},
      {'icon': Icons.photo_camera, 'label': 'Art galleries for'},
      {'icon': Icons.audiotrack, 'label': 'Dance performances to'},
      {'icon': Icons.mic, 'label': 'Live comedy shows for'},
      {'icon': Icons.theater_comedy, 'label': 'Theatrical'},
      {'icon': Icons.family_restroom, 'label': 'Family-friendly events'},
      {'icon': Icons.tv, 'label': 'Live stream'},
    ];
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search for events',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.list),
                      onPressed: () {},
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const Text('Categories', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22)),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.separated(
                  itemCount: categories.length,
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final cat = categories[index];
                    return ListTile(
                      leading: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(10),
                        child: Icon(cat['icon'] as IconData, color: Colors.black),
                      ),
                      title: Text(cat['label'] as String, style: const TextStyle(fontWeight: FontWeight.w600)),
                      trailing: const Icon(Icons.chevron_right, color: Colors.black),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => const ChatScreen()),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Hom'),
          BottomNavigationBarItem(icon: Icon(Icons.explore), label: 'Disco'),
          BottomNavigationBarItem(icon: Icon(Icons.event), label: 'Events'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Com'),
        ],
        selectedItemColor: Colors.black,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
} 