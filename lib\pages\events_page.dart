import 'package:flutter/material.dart';
import 'home_screen.dart';
import 'events_search_screen.dart';
import 'community_screen.dart';
import 'event_details_page.dart';
import '../widgets/sidebar_drawer.dart';

class EventsPage extends StatefulWidget {
  const EventsPage({super.key});

  @override
  State<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage> {
  int _selectedIndex = 2;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  // All events data
  final List<Map<String, String>> _allEvents = [
    {
      'imageUrl': 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?auto=format&fit=crop&w=400&q=80',
      'title': 'Cultural Heritage Festival',
      'date': 'Saturday, April 15, 2024',
      'time': '10:00 AM - 6:00 PM',
      'location': 'Central Park, New York City',
      'description': 'Join us for a day of cultural exploration and celebration at the Cultural Heritage Festival. Experience traditional music, dance, and cuisine from around the world, all in the heart of New York City. This event is open to all ages and offers a unique opportunity to learn and appreciate the diverse cultures that make up our global community.',
    },
    {
      'imageUrl': 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&q=80',
      'title': 'Summer Music Fest',
      'date': 'August 12, 2023',
      'time': '10:00 AM - 6:00 PM',
      'location': 'Central Park, New York',
      'description': 'Join us for a day filled with live music performances by top artists in a vibrant outdoor setting.',
    },
    {
      'imageUrl': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?auto=format&fit=crop&w=400&q=80',
      'title': 'Gourmet Cooking Class',
      'date': 'September 5, 2023',
      'time': '2:00 PM - 5:00 PM',
      'location': 'Culinary Institute, Chicago',
      'description': 'Learn the art of gourmet cooking with expert chefs in an interactive class setting.',
    },
    {
      'imageUrl': 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?auto=format&fit=crop&w=400&q=80',
      'title': 'Tech Innovators Conference',
      'date': 'October 20, 2023',
      'time': '9:00 AM - 6:00 PM',
      'location': 'Silicon Valley, California',
      'description': 'Discover the latest tech innovations and meet industry leaders at this premier conference.',
    },
    {
      'imageUrl': 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?auto=format&fit=crop&w=400&q=80',
      'title': 'Art Gallery Opening',
      'date': 'November 8, 2023',
      'time': '6:00 PM - 9:00 PM',
      'location': 'Modern Art Museum, Los Angeles',
      'description': 'Experience contemporary art from emerging artists in this exclusive gallery opening event.',
    },
    {
      'imageUrl': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=400&q=80',
      'title': 'Yoga Retreat Weekend',
      'date': 'December 2, 2023',
      'time': '8:00 AM - 6:00 PM',
      'location': 'Wellness Center, Austin',
      'description': 'Rejuvenate your mind and body with a peaceful yoga retreat in a serene environment.',
    },
  ];

  // Filtered events based on search
  List<Map<String, String>> get _filteredEvents {
    if (_searchQuery.isEmpty) {
      return _allEvents;
    }
    return _allEvents.where((event) {
      return event['title']!.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             event['location']!.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             event['description']!.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  void _onItemTapped(int index) {
    if (index == 0) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    } else if (index == 1) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => EventsSearchScreen(
            scrollController: ScrollController(),
          ),
        ),
      );
    } else if (index == 3) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const CommunityScreen()),
      );
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      drawer: const SidebarDrawer(),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: _isSearching
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                    _searchQuery = '';
                  });
                },
              )
            : IconButton(
                icon: const Icon(Icons.menu, color: Colors.black),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                decoration: const InputDecoration(
                  hintText: 'Search events...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(color: Colors.grey),
                ),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18,
                ),
              )
            : Row(
                children: [
                  Icon(Icons.public, color: Colors.black, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'GlobalRoots',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
        actions: _isSearching
            ? [
                if (_searchQuery.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear, color: Colors.black),
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                        _searchQuery = '';
                      });
                    },
                  ),
              ]
            : [
                IconButton(
                  icon: const Icon(Icons.search, color: Colors.black),
                  onPressed: () {
                    setState(() {
                      _isSearching = true;
                    });
                  },
                ),
              ],
      ),
      body: _isSearching && _filteredEvents.isEmpty && _searchQuery.isNotEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No events found',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Try searching with different keywords',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _filteredEvents.length,
              itemBuilder: (context, index) {
                final event = _filteredEvents[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: _buildEventCard(
                    imageUrl: event['imageUrl']!,
                    title: event['title']!,
                    date: event['date']!,
                    time: event['time']!,
                    location: event['location']!,
                    description: event['description']!,
                  ),
                );
              },
            ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Disco'),
          BottomNavigationBarItem(icon: Icon(Icons.event), label: 'Events'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
        selectedItemColor: Colors.black,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }

  Widget _buildEventCard({
    required String imageUrl,
    required String title,
    required String date,
    required String time,
    required String location,
    required String description,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => EventDetailsPage(
              title: title,
              imageUrl: imageUrl,
              date: date,
              time: time,
              location: location,
              description: description,
            ),
          ),
        );
      },
      child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event Image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: Image.network(
              imageUrl,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 120,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image, size: 50, color: Colors.grey),
                );
              },
            ),
          ),
          
          // Event Details
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  date,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  location,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 12),
                
                // RSVP Button
                Container(
                  width: 60,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Center(
                    child: Text(
                      'RSVP',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
    );
  }
}
