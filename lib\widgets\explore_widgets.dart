import 'package:flutter/material.dart';

class ExploreCategory extends StatelessWidget {
  final IconData icon;
  final String label;
  const ExploreCategory({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Color.fromARGB(255, 197, 193, 193),
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.all(12),
          child: Icon(icon, color: Colors.black),
        ),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
}

class ExploreCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String subtitle;
  final bool favorite;
  const ExploreCard({
    required this.imageUrl,
    required this.title,
    required this.subtitle,
    required this.favorite,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 197, 193, 193),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 12.0, left: 11.0, right: 11.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[850], // Dark gray background
                borderRadius: BorderRadius.circular(16),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.network(
                  imageUrl,
                  height: 80,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Icon(
              favorite ? Icons.favorite : Icons.favorite_border,
              color: favorite
                  ? const Color.fromARGB(255, 19, 19, 19)
                  : const Color.fromARGB(255, 248, 248, 248),
              size: 20,
            ),
          ),
          Positioned(
            bottom: 8,
            left: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  subtitle,
                  style: const TextStyle(
                    color: Colors.black54,
                    fontSize: 11,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                    fontSize: 13,
                    shadows: [
                      Shadow(color: Colors.white, blurRadius: 2),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
