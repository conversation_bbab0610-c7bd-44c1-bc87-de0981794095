import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/base_url.dart';

class AuthService {
  static Future<Map<String, dynamic>> register({
    required String fullName,
    required String phone,
    required String password,
    required String email,
    required String hometown,
  }) async {
    final url = Uri.parse('${AppConfig.baseUrl}/api/v1/auth/signup');
    final headers = {'Content-Type': 'application/json'};
    final body = {
      "fullName": fullName,
      "phone": phone,
      "password": password,
      "email": email,
      "hometown": hometown,
    };
    final response = await http.post(url, headers: headers, body: jsonEncode(body));
    return {
      'statusCode': response.statusCode,
      'body': response.body,
    };
  }

  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final url = Uri.parse('${AppConfig.baseUrl}/api/v1/auth/login');
    final headers = {'Content-Type': 'application/json'};
    final body = {
      "email": email,
      "password": password,
    };
    final response = await http.post(url, headers: headers, body: jsonEncode(body));
    return {
      'statusCode': response.statusCode,
      'body': response.body,
    };
  }

  static Future<Map<String, dynamic>> forgotPassword({
    required String email,
  }) async {
    final url = Uri.parse('${AppConfig.baseUrl}/api/v1/auth/forgot-password');
    final headers = {'Content-Type': 'application/json'};
    final body = {"email": email};
    final response = await http.post(url, headers: headers, body: jsonEncode(body));
    return {
      'statusCode': response.statusCode,
      'body': response.body,
    };
  }
}
